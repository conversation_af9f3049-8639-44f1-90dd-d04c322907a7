<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php wp_head(); ?>
    <style>
        /* Normal header styles */
        /* .smort-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 auto;
            width: 100%;
        } */

        /* Sticky header styles */
        .sticky-header {
            display: none !important;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) scale(0);
            width: 0;
            background-color: #201c1c;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            padding: 10px 20px;
            transition: transform 0.2s ease-out, width 0.2s ease-out;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            opacity: 0;
            border-radius: 10px;
            border: 1px solid #312f2f;
        }

        .sticky-header.active {
            transform: translateX(-50%) scale(1);
            /* Grow from center */
            width: 95%;
            /* Expand to full width */
            opacity: 1;
            /* Make visible */
        }

        img.modal-logo-mobile {
            width: 100%;
            position: absolute;
            bottom: -25%;
            left: 0;
        }

        .sticky-header .logo {
            display: flex;
            align-items: center;
        }

        .sticky-header .burger-menu {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            max-width: 50px;
            align-items: flex-end;
        }

        .sticky-header .burger-menu span {
            height: 2px;
            width: 100%;
            margin: 4px 0;
        }

        .sticky-header .burger-menu span {
            background: #fff;
        }

        .burger-menu.sticky-burger-menu span:nth-of-type(2) {
            width: 80%;
        }

        .right-sticky button {
            background: var(--accentColor);
            border-radius: 5px;
            color: #fff;
            border: 0px;
            width: 250px;
            text-align: left;
            font-family: var(--fontFamily);
            padding: 8px 10px;
            position: relative;
        }

        .right-sticky button:after {
            content: url('/wp-content/themes/smort_commerce/img/arrow-up-right.svg');
            position: absolute;
            right: 10px;
            filter: invert(1);
            transition: transform 0.2s ease-in-out;
        }

        .right-sticky button:hover:after {
            transform: rotate(45deg);
        }

        .right-sticky {
            display: flex;
            gap: 30px;
            width: 350px;
        }

        /* Preserve your button and other content */
        .sticky-header .smort-cart {
            display: flex;
            align-items: center;
        }

        .smort-footer {
            background-color: <?php echo get_field('secondary_color', 'option'); ?>;
        }

        .smort-nav ul li a:hover,
        .smort-nav ul li a:active {
            color: #fff;
        }

        .smort-logo img {
            height: <?php echo get_field('logo_height', 'option'); ?>px;
        }

        <?php if (get_field('transparent_header', 'option')): ?>.smort-header {
            background: none;
            position: absolute;
            /* width: calc(100% - 2%); */
            padding: 1%;
            z-index: 999999;
        }

        body {
            margin-top: 0;
        }

        <?php endif; ?><?php if (get_field('enable_topbar', 'option')): ?>.smort-topbar {
            background-color: <?php echo get_field('topbar_background_color', 'option'); ?>;
        }

        <?php endif; ?>

        /* Mobile Menu Styles */
        .burger-menu-container {
            display: none;
            position: absolute;
            top: 0;
            left: 10px;
            width: 33%;
            display: flex;
            justify-content: flex-start !important;
            align-items: center;
        }

        .burger-menu {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            padding: 0px;
            max-width: 50px;
            position: initial;
        }

        .burger-menu span {
            background: #000;
            height: 2px;
            width: 100%;
            margin: 4px 0;
        }

        .mobile-nav {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url(/wp-content/uploads/2024/11/<EMAIL>), #000;
            background-size: cover;
            background-position: right center;
            z-index: 1000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            transition: opacity 0.3s ease-in-out;
            opacity: 0;
            pointer-events: none;
            margin-top: 0px;
        }

        .mobile-nav.active {
            display: flex;
            opacity: 1;
            pointer-events: auto;
        }

        .mobile-nav-inner li a {
            font-size: 3rem !important;
            line-height: 1.2;
        }

        .mobile-nav-inner li {
            text-align: left;
        }

        .mobile-nav-inner {
            text-align: center;
            width: 90%;
            overflow-y: auto;
            text-align: left;
            margin-top: -30%;
        }

        .close-nav {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 30px;
            color: #fff;
            cursor: pointer;
            background: var(--accentColor);
            padding: 10px 20px;
            border-radius: 50%;
        }

        .mobile-nav ul {
            list-style-type: none;
            padding: 0;
        }

        .mobile-nav ul li {
            margin: 10px 0;
        }

        .mobile-nav ul li a {
            color: #fff;
            text-decoration: none;
            font-size: 20px;
            background: -webkit-linear-gradient(#fbfbfb, #b7b7b7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .mobile-nav ul li ul {
            display: none;
            list-style-type: none;
            padding: 0;
        }

        .mobile-nav ul li.active>ul {
            display: block;
        }

        @media (max-width: 992px) {

            .burger-menu-container,
            .smort-logo,
            .smort-cart {
                width: 33%;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 33%;
                display: flex;
                align-items: center;
                position: relative;
                height: 50px;
            }

            .main-navigation {
                display: none;
            }

            .smort-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* width: calc(100% - 25px); */
            }

            div#burger-menu span:nth-of-type(2) {
                width: 70%;
            }

            .burger-menu span {
                background: #fff;
            }

            .burger-menu {
                align-items: flex-start;
            }

            .mobile-nav {
                justify-content: flex-start;
                direction: ltr;
                padding-top: 5%;
            }

            .mobile-nav-inner li a {
                font-size: 1.7rem !important;
            }

            .burger-menu {
                z-index: 10 !important;
            }

            .menu-menu-1-container ul {
                text-align: left;
            }

            .contact-overlaymenu {
                padding-left: 5px !important;
            }

            ul.sub-menu a {
                font-size: 1.6rem !important;
            }

            .right-sticky button {
                display: none;
            }

            .right-sticky {
                justify-content: end;
            }

            .sticky-header img {
                filter: invert(1);
                height: 20px !important;
            }
        }



        .mobile-nav ul li {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.5s ease forwards;
            animation-delay: var(--delay, 0s);
        }

        /* Animation for fading and sliding in */
        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Apply a delay to each menu item */
        .mobile-nav.active ul li:nth-child(1) {
            --delay: 0.1s;
        }

        .mobile-nav.active ul li:nth-child(2) {
            --delay: 0.2s;
        }

        .mobile-nav.active ul li:nth-child(3) {
            --delay: 0.3s;
        }

        .mobile-nav.active ul li:nth-child(4) {
            --delay: 0.4s;
        }

        .mobile-nav.active ul li:nth-child(5) {
            --delay: 0.5s;
        }


        .smort-pristabell-features-extra li {
            position: relative;
            padding-left: 25px;
            background: url(/wp-content/uploads/2024/11/check-9.svg) no-repeat left center;
            background-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .forklaring-content {
            display: none;
            margin: 5px;
            padding: 0px 10px;
            border: 1px solid #828282;
            border-radius: 10px;
        }

        span.forklaring-toggle {
            position: absolute;
            right: 20px;
            cursor: pointer;
        }

        .forklaring-content p {
            font-size: 14px;
        }

        .mobile-nav-inner .menu-menu-1-container ul {
            border: 0px;
            text-align: left;
        }

        .menu-item-has-children>.menu-arrow {
            margin-left: 10px;
            cursor: pointer;
            display: inline-block;
        }

        .mobile-menu .sub-menu {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .mobile-menu .sub-menu.open {
            display: block;
            opacity: 1;
        }

        img.menu-arrow {
            width: 40px !important;
            transform: rotate(-45deg);
        }

        .menu-arrow {
            margin-left: 10px;
            vertical-align: middle;
            /* Aligns the image with the text */
        }

        .mobile-nav-inner ul.sub-menu a {
            font-size: 2.5rem;
        }

        .contact-overlaymenu a {
            color: #fff;
            text-decoration: none;
            border: 1px solid #353535 !important;
            padding: 10px;
            border-radius: 5px;
            font-family: var(--fontFamily);
            width: 150px;
            text-align: center;
            font-weight: 600;
        }

        .contact-overlaymenu {
            margin-top: 15px;
            padding-left: 0px;
            margin-bottom: 50px;
            display: flex;
            gap: 20px;
        }

        .mobile-nav-logo {
            position: fixed;
            bottom: 0;
            left: 0;
            /* width: 100vw; */
            height: auto;
            max-height: 40vh;
            /* Display only the top 60% of the image */
            overflow: hidden;
            z-index: 1001;
            /* Ensure it's above other elements */
            transform: translateY(40%);
            /* Show only the top 60% */
        }

        .mobile-nav-logo img {
            width: 100%;
            /* Make the image fill the width of the viewport */
            height: auto;
        }
    </style>


    <script>
        /*Sticky */
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('.smort-header');
            const stickyHeader = document.createElement('div');
            const screenWidth = window.innerWidth || document.documentElement.clientWidth;

            // Only create the sticky header for desktop devices
            stickyHeader.classList.add('sticky-header');
            stickyHeader.innerHTML = `
            <div class="logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>" style="height: 40px;">
                    <?php else: ?>
                        Smort
                    <?php endif; ?>
                </a>
            </div>
			<div class="right-sticky">
				<button class="darksoul-button1" type="button" id="openModal">
                    Book a job
                </button>

                 <?php echo do_shortcode('[woo_j_cart_count]'); ?>
				<div class="burger-menu sticky-burger-menu">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
        `;
            document.body.appendChild(stickyHeader);

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) { // Adjust as needed for when the sticky header should appear
                    stickyHeader.classList.add('active');
                } else {
                    stickyHeader.classList.remove('active');
                }
            });

            const burgerMenu = document.getElementById('burger-menu');
            const stickyBurgerMenu = document.querySelector('.sticky-header .burger-menu');
            const mobileNav = document.getElementById('mobile-nav');
            const closeNav = document.getElementById('close-nav');

            // Open the mobile nav when the burger menu or sticky burger menu is clicked
            [burgerMenu, stickyBurgerMenu].forEach(menu => {
                if (menu) {
                    menu.addEventListener('click', function() {
                        mobileNav.classList.add('active');
                    });
                }
            });

            // Close the mobile nav when the close button is clicked
            closeNav.addEventListener('click', function() {
                mobileNav.classList.remove('active');
            });

            // Close the mobile nav when clicking outside the menu
            document.addEventListener('click', function(event) {
                if (!mobileNav.contains(event.target) && !burgerMenu.contains(event.target) && !stickyBurgerMenu.contains(event.target)) {
                    mobileNav.classList.remove('active');
                }
            });
        });

        window.addEventListener('scroll', function() {
            const element = document.getElementById('smort-colorchange');
            const rect = element.getBoundingClientRect();
            const elementHeight = element.offsetHeight;
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;

            // Calculate when 20% of the element is in view
            const triggerInPoint = windowHeight - elementHeight * 0.4;
            const triggerOutPoint = elementHeight * 0.2;

            // Check if 20% of the div is in view to add the new color
            if (rect.top <= triggerInPoint && rect.bottom >= triggerOutPoint) {
                element.classList.add('new-color');
            }
            // Check if less than 20% of the div is visible to revert to the default color
            else {
                element.classList.remove('new-color');
            }
        });
        /* Infinity scroll smort */
    </script>
</head>

<body <?php body_class(get_field('transparent_header', 'option') ? 'transparent-header' : ''); ?>>
    <?php if (get_field('enable_topbar', 'option')): ?>
        <div class="smort-topbar">
            <?php echo esc_html(get_field('topbar_text', 'option')); ?>
        </div>
    <?php endif; ?>
    <header class="smort-header smort-header-<?php echo get_field('header_style', 'option'); ?>">
        <?php if (get_field('header_style', 'option') == 'style_1'): ?>
            <div class="header-top-wrapper">
                <div class="header-top">
                    <div class="smort-logo">
                        <a href="<?php echo esc_url(home_url('/')); ?>">
                            <?php $logo = get_field('logo', 'option'); ?>
                            <?php if ($logo): ?>
                                <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                            <?php else: ?>
                                Smort
                            <?php endif; ?>
                        </a>
                    </div>
                    <div>

                        <?php echo do_shortcode('[swcs_search]'); ?>
                    </div>
                    <div class="smort-cart">
                        <?php echo do_shortcode('[smort_cart]'); ?>
                    </div>
                </div>
            </div>
            <div class="header-bottom">
                <div class="header-bottom-inner">

                    <nav class="main-navigation smort-nav smort-nav-centered">
                        <?php wp_nav_menu(array('smort_commerce' => 'header-menu')); ?>
                    </nav>
                    <button class="promotion-global-btn variant-primary customer-service">Kundservice
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 6.723 7.438">
                                <g id="user_6_" data-name="user (6)" transform="translate(0.5 0.5)">
                                    <path id="Path_19" data-name="Path 19" d="M9.723,17.146v-.715A1.431,1.431,0,0,0,8.292,15H5.431A1.431,1.431,0,0,0,4,16.431v.715" transform="translate(-4 -10.708)" fill="none" stroke="#172949" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" />
                                    <circle id="Ellipse_5" data-name="Ellipse 5" cx="1.48" cy="1.48" r="1.48" transform="translate(1.48 0)" fill="none" stroke="#172949" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" />
                                </g>
                            </svg>

                        </div>
                    </button>
                </div>

            <?php elseif (get_field('header_style', 'option') == 'style_2'): ?>
                <div class="burger-menu-container">
                    <div class="burger-menu" id="burger-menu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
                <nav class="main-navigation smort-nav smort-nav-left">
                    <?php wp_nav_menu(array('smort_commerce' => 'header-menu')); ?>
                </nav>
                <div class="smort-logo">
                    <a href="<?php echo esc_url(home_url('/')); ?>">
                        <?php $logo = get_field('logo', 'option'); ?>
                        <?php if ($logo): ?>
                            <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                        <?php else: ?>
                            Smort
                        <?php endif; ?>
                    </a>
                </div>
                <div class="smort-cart">
                    <a class="book-demo-smort">Call to action</a>
                </div>
            <?php endif; ?>

            <!-- Mobile Menu Elements -->
            <nav class="mobile-nav" id="mobile-nav">
                <div class="contact-overlaymenu">

                </div>
                <div class="mobile-nav-inner">
                    <div class="contact-overlaymenu">
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                        <a href="tel:+4631211331">+4631 - 00 00 00</a>
                    </div>
                    <button class="close-nav" id="close-nav">&times;</button>
                    <?php wp_nav_menu(array('smort_commerce' => 'header-menu', 'menu_class' => 'mobile-menu')); ?>
                </div>
            </nav>

    </header>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-button');
            const caseStudyItems = document.querySelectorAll('.case-study-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const category = button.getAttribute('data-category');

                    // Remove active class from all buttons and add to clicked button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    // Show/hide case study items
                    caseStudyItems.forEach(item => {
                        if (category === 'all') {
                            item.style.display = 'block';
                        } else {
                            if (item.classList.contains(category)) {
                                item.style.display = 'block';
                            } else {
                                item.style.display = 'none';
                            }
                        }
                    });
                });
            });
        });


        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.mobile-menu .menu-item-has-children > a');

            menuItems.forEach(function(item) {
                // Check if the arrow already exists
                if (!item.parentElement.querySelector('.menu-arrow')) {
                    // Create the arrow image element
                    const arrow = document.createElement('img');
                    arrow.classList.add('menu-arrow');
                    arrow.src = '/wp-content/uploads/2024/11/menu-arrow.svg'; // Path to your image
                    arrow.alt = 'Expand menu'; // Optional alt text
                    arrow.style.width = '16px'; // Adjust size as needed
                    arrow.style.cursor = 'pointer'; // Make it clickable
                    arrow.style.transition = 'transform 0.3s ease'; // Add transition for rotation

                    // Append the image inside the link but positioned after the text
                    item.style.display = 'inline-flex';
                    item.style.alignItems = 'center';
                    item.appendChild(arrow);

                    // Add click event for the arrow
                    arrow.addEventListener('click', function(e) {
                        e.preventDefault();
                        const subMenu = item.nextElementSibling;

                        if (subMenu) {
                            subMenu.classList.toggle('open');
                            if (subMenu.classList.contains('open')) {
                                subMenu.style.display = 'block';
                                subMenu.style.opacity = '1';
                                subMenu.style.transition = 'opacity 0.3s';
                                arrow.style.transform = 'rotate(90deg)'; // Rotate the arrow
                            } else {
                                subMenu.style.opacity = '0';
                                setTimeout(() => {
                                    subMenu.style.display = 'none';
                                }, 300);
                                arrow.style.transform = 'rotate(0deg)'; // Reset rotation
                            }
                        }
                    });
                }
            });
        });


        document.addEventListener("DOMContentLoaded", function() {
            var items = document.querySelectorAll('.addons-item');

            items.forEach(function(item) {
                var header = item.querySelector('.addons-header');
                header.addEventListener('click', function() {
                    // Om objektet är öppet, stäng det
                    if (item.classList.contains('open')) {
                        item.classList.remove('open');
                    } else {
                        // Stäng alla andra accordeoner
                        items.forEach(function(i) {
                            i.classList.remove('open');
                        });

                        // Öppna den klickade accordion
                        item.classList.add('open');
                    }
                });
            });
        });




        document.addEventListener('DOMContentLoaded', function() {
            const openModal = document.getElementById('openModal');
            const modal = document.getElementById('bookJobModal');
            const closeModal = modal.querySelector('.close-modal');

            // Open modal on button click
            openModal.addEventListener('click', function() {
                modal.style.display = 'flex';
            });

            // Close modal on close button click
            closeModal.addEventListener('click', function() {
                modal.style.display = 'none';
            });

            // Close modal when clicking outside of modal content
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    </script>


    <!-- Modal Popup -->
    <div id="bookJobModal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>CTA Container</h2>
            <?php echo do_shortcode('[contact-form-7 id="cbdd57a" title="Kontaktformulär]'); ?>
        </div>
    </div>

</body>

</html>