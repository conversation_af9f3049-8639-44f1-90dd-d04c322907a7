body.single-product {
  main {
    background-color: var(--accentcolor3);
  }

  /* Content Single Product */
  .content-single-product {
    display: flex;
    justify-content: space-between;
    margin-inline: var(--side-margin);
    > div {
      float: none !important;
    }
  }
  .woocommerce-product-gallery__wrapper:has(:first-child:not(:last-child)) {
    display: grid;
    grid-template-columns: 140px 1fr;
    gap: 24px;
    & div:first-child {
      order: 2;
    }
    .woocommerce-product-gallery__image {
      width: 100% !important;
      display: flex !important;
      flex-direction: column;
      gap: 12px;

      img {
        border-radius: var(--border-radius-sm);
      }
    }
  }
  .product_title.entry-title {
    font-size: 40px;
  }
  .single-product-price-container {
    p {
      font-size: 1rem !important;
      color: var(--black) !important;
    }
  }
  .single-product-information-table {
    dl {
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      gap: 6px;
      div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: var(--white);
        padding: 8px 14px;
      }
    }
  }

  /* Cart */
  .cart {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /* Quantity controls mobile */
  .quantity {
    background-color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    display: flex;
  }

  .quantity .minus,
  .quantity .plus {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    padding: 0;
    cursor: pointer;
    height: 45px;
  }

  .quantity .qty {
    border: none;
    font-size: 1.5rem !important;
  }
  .single_add_to_cart_button {
    background-color: var(--primary-color) !important;
    width: 100%;
    border-radius: var(--border-radius) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    .svg-container {
      background-color: var(--white);
      padding: 8px 12px;
      border-radius: var(--border-radius);
    }
  }

  .accordion-tab {
    .accordion-tab-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;
      width: 100%;
      border: none;
      background: transparent;
      font-size: 1.25rem;
      border-bottom: 1px solid var(--black);
      cursor: pointer;
    }
    .accordion-tab-content {
      height: 0;
      transition: height var(--delay) ease-in-out;
      overflow: hidden;
    }
    &:has([aria-expanded='true']) {
      svg {
        transform: rotate(180deg);
      }
      .accordion-tab-content {
        height: auto;
        height: calc-size(auto);
      }
    }
  }

  /* Related Products  */
  section.related.products {
    margin-inline: var(--side-margin);
    h2 {
      padding-bottom: 24px;
      border-bottom: 1px solid var(--black);
    }
  }
  background-color: hotpink;

  form.variations_form {
    .variation-button {
      background-color: hotpink;
      text-transform: uppercase;
    }
  }
}
