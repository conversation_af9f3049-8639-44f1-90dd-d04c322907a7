<?php

/**
 * Single Product Price
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/price.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.0.0
 */

if (! defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

global $product;



$table_rows = [
	'sku' => $product->get_sku(),
	'antal_fp' => $product->get_attribute('antal_fp'),
	'material' => $product->get_attribute('material'),
	'country_of_origin' => $product->get_attribute('country_of_origin'),
];

function translateTableTitle($key)
{
	$title = '';
	switch ($key) {
		case 'sku':
			$title = 'Artikelnummer';
			break;
		case 'antal_fl':
			$title = 'Antal per förpackning';
			break;
		case 'material':
			$title = 'Material';
			break;
		case 'country_of_origin':
			$title = 'Tillverkningsland';
			break;
		default:
			$title = '';
			break;
	}
	return $title;
}
?>
<div class="single-product-price-container">
	<p class="<?php echo esc_attr(apply_filters('woocommerce_product_price_class', 'price')); ?>">DITT PRIS: <span style="color: var(--secondary-color); font-size:30px;">
			<?php echo $product->get_price_html(); ?>
		</span>
	</p>
</div>

<div class=" single-product-information-table">
	<dl>
		<?php foreach ($table_rows as $key => $row) : ?>
			<?php if (!empty($key) && !empty($row)) : ?>
				<div>
					<dt><?= translateTableTitle($key) ?></dt>
					<dd><?= $row ?> </dd>
				</div>
			<?php endif ?>
		<?php endforeach ?>
	</dl>
</div>