<?php

/**
 * Simple product add to cart
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/add-to-cart/simple.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.0.1
 */

defined('ABSPATH') || exit;

global $product;

if (! $product->is_purchasable()) {
	return;
}

echo wc_get_stock_html($product); // WPCS: XSS ok.

if ($product->is_in_stock()) : ?>

	<?php do_action('woocommerce_before_add_to_cart_form'); ?>

	<form class="cart" action="<?php echo esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())); ?>" method="post" enctype='multipart/form-data'>
		<?php do_action('woocommerce_before_add_to_cart_button'); ?>

		<?php
		do_action('woocommerce_before_add_to_cart_quantity');

		woocommerce_quantity_input(
			array(
				'min_value'   => apply_filters('woocommerce_quantity_input_min', $product->get_min_purchase_quantity(), $product),
				'max_value'   => apply_filters('woocommerce_quantity_input_max', $product->get_max_purchase_quantity(), $product),
				'input_value' => isset($_POST['quantity']) ? wc_stock_amount(wp_unslash($_POST['quantity'])) : $product->get_min_purchase_quantity(), // WPCS: CSRF ok, input var ok.
			)
		);

		do_action('woocommerce_after_add_to_cart_quantity');
		?>

		<button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>" class="single_add_to_cart_button button alt<?php echo esc_attr(wc_wp_theme_get_element_class_name('button') ? ' ' . wc_wp_theme_get_element_class_name('button') : ''); ?>"><?php echo esc_html($product->single_add_to_cart_text()); ?>
			<div class="svg-container">
				<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="23" viewBox="0 0 20 23">
					<defs>
						<pattern id="pattern" preserveAspectRatio="none" width="100%" height="100%" viewBox="0 0 450 512">
							<image width="450" height="512" xlink:href="data:image/png;base64,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" />
						</pattern>
					</defs>
					<rect id="shopping-bag-icon-450x512-4w8uknzm" width="20" height="23" fill="url(#pattern)" />
				</svg>

			</div>
		</button>

		<?php do_action('woocommerce_after_add_to_cart_button'); ?>
	</form>

	<?php do_action('woocommerce_after_add_to_cart_form'); ?>

<?php endif; ?>