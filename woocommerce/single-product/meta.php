<?php

/**
 * Single Product Meta
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/meta.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     3.0.0
 */

if (! defined('ABSPATH')) {
	exit;
}

global $product;

// Get product tabs
$product_tabs = apply_filters('woocommerce_product_tabs', array());
if (!empty($product_tabs)) :
	$expandedKey = null;
?>

	<div class="smort-accordion-tabs">
		<?php foreach ($product_tabs as $key => $product_tab) :
			// Translate tab titles
			$isExpanded = isset($expandedKey) && $expandedKey === $Key;
			$tab_title = $product_tab['title'];
			switch ($key) {
				case 'description':
					$tab_title = 'Beskrivning';
					break;
				case 'additional_infomation':
					$tab_title = 'Mer Information';
					break;
				case 'reviews':
					$tab_title = 'Recensioner';
					break;
				case 'usage':
					$tab_title = 'Användning';
					break;
				case 'ingredients':
					$tab_title = 'Ingredienser';
					break;
				default:
					$tab_title = 'Övrigt';
					break;
			}
		?>

			<div class="accordion-tab">
				<button class="accordion-tab-title" aria-controls="<?= 'accordion-tab-content-' . $key; ?>" aria-expanded="false">
					<span>
						<?= esc_html($tab_title); ?>
					</span>
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
						<path d="m6 9 6 6 6-6" />
					</svg>
				</button>

				<div class="accordion-tab-content accordion-tab-content-<?= $key; ?>" id="accordion-tab-content-<?= $key; ?>" aria-labelledby="accordion-title-<?= $key; ?>">
					<?php
					if (isset($product_tab['callback'])) {
						call_user_func($product_tab['callback'], $key, $product_tab);
					}
					?>
				</div>
			</div>

		<?php endforeach ?>
	</div>

<?php endif; ?>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const accordions = document.querySelectorAll('.accordion-tab-title');

		accordions.forEach(title => {
			title.addEventListener('click', function() {
				const isExpanded = title.getAttribute('aria-expanded');
				if (isExpanded === 'true') {
					title.setAttribute('aria-expanded', 'false')
				} else {
					title.setAttribute('aria-expanded', 'true')
				}
			})
		})
	})
</script>