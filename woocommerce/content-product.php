<?php

/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 */

defined('ABSPATH') || exit;

global $product;

// Ensure visibility.
if (empty($product) || ! $product->is_visible()) {
	return;
}

$stock_status = $product->get_stock_status();
$backorders = $product->backorders_allowed();
$stock_text = '';
$stock_class = '';

if ($stock_status === 'instock' && !$backorders) {
	$stock_text = 'Leverans 1-2 dagar';
	$stock_class = 'stock-green';
} elseif ($stock_status === 'onbackorder') {
	$stock_text = 'Leverans 6-10 dagar';
	$stock_class = 'stock-yellow';
} else {
	$stock_text = 'Slut i lager';
	$stock_class = 'stock-red';
}

$product_amount_fp = $product->get_attribute('antal-forpackning') ?? null;

?>
<li <?php wc_product_class('', $product); ?>>
	<?php
	/**
	 * Hook: woocommerce_before_shop_loop_item.
	 *
	 * @hooked woocommerce_template_loop_product_link_open - 10
	 */
	do_action('woocommerce_before_shop_loop_item');

	/**
	 * Hook: woocommerce_before_shop_loop_item_title.
	 *
	 * @hooked woocommerce_show_product_loop_sale_flash - 10
	 * @hooked woocommerce_template_loop_product_thumbnail - 10
	 */
	do_action('woocommerce_before_shop_loop_item_title');

	/**
	 * Hook: woocommerce_shop_loop_item_title.
	 *
	 * @hooked woocommerce_template_loop_product_title - 10
	 */
	do_action('woocommerce_shop_loop_item_title');

	/**
	 * Hook: woocommerce_after_shop_loop_item_title.
	 *
	 * @hooked woocommerce_template_loop_rating - 5
	 * @hooked woocommerce_template_loop_price - 10
	 */
	?><div class="related-products-item-information">
		<div class="related-products-item-price">

			<?php do_action('woocommerce_after_shop_loop_item_title'); ?>
		</div>
		<div class="stock-status <?php echo esc_attr($stock_class); ?>">
			<span class="stock-dot"></span> <?php echo esc_html($stock_text); ?>
		</div>
	</div>
	<?php
	/**
	 * Hook: woocommerce_after_shop_loop_item.
	 *
	 * @hooked woocommerce_template_loop_product_link_close - 5
	 * @hooked woocommerce_template_loop_add_to_cart - 10
	 */
	?>
	<div class="related-products-item-add-to-cart">
		<?php do_action('woocommerce_after_shop_loop_item');
		?>
		<?php if ($product_amount_fp) : ?>
			<p class="amount-fp">
				<?= 'Antal/fp ' . esc_html($product_amount_fp) ?>
			</p>
		<?php endif ?>
	</div>
</li>