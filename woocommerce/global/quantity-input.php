<?php
/**
 * Quantity inputs
 *
 * @see       https://docs.woocommerce.com/document/template-structure/
 * @package   WooCommerce/Templates
 * @version   4.0.0
 */

defined( 'ABSPATH' ) || exit;

if ( $max_value && $min_value === $max_value ) {
    ?>
    <input type="hidden" id="<?php echo esc_attr( $input_id ); ?>" class="qty" name="<?php echo esc_attr( $input_name ); ?>" value="<?php echo esc_attr( $min_value ); ?>" />
    <?php
} else {
    ?>
    <div class="quantity">
        <button type="button" class="minus">-</button>
        <input
            type="text"
            id="<?php echo esc_attr( $input_id ); ?>"
            class="input-text qty text"
            step="<?php echo esc_attr( $step ); ?>"
            min="<?php echo esc_attr( $min_value ); ?>"
            max="<?php echo esc_attr( 0 < $max_value ? $max_value : '' ); ?>"
            name="<?php echo esc_attr( $input_name ); ?>"
            value="<?php echo esc_attr( $input_value ); ?>"
            title="<?php echo esc_attr_x( 'Qty', 'Product quantity input tooltip', 'woocommerce' ); ?>"
            size="4"
            inputmode="<?php echo esc_attr( $inputmode ); ?>" />
        <button type="button" class="plus">+</button>
    </div>
    <?php
}
?>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Select all quantity input containers
    const quantities = document.querySelectorAll('.quantity');

    quantities.forEach(function (quantity) {
        const input = quantity.querySelector('.qty');
        const minusButton = quantity.querySelector('.minus');
        const plusButton = quantity.querySelector('.plus');

        // Handle plus button click
        plusButton.addEventListener('click', function () {
            let currentValue = parseInt(input.value) || 0; // Parse as integer
            const step = parseInt(input.getAttribute('step')) || 1;
            const max = parseInt(input.getAttribute('max')) || Infinity;

            if (currentValue + step <= max) {
                input.value = currentValue + step; // Update input value as integer
                input.dispatchEvent(new Event('change')); // Trigger change event
            }
        });

        // Handle minus button click
        minusButton.addEventListener('click', function () {
            let currentValue = parseInt(input.value) || 0; // Parse as integer
            const step = parseInt(input.getAttribute('step')) || 1;
            const min = parseInt(input.getAttribute('min')) || 0;

            if (currentValue - step >= min) {
                input.value = currentValue - step; // Update input value as integer
                input.dispatchEvent(new Event('change')); // Trigger change event
            }
        });
    });
});

</script>