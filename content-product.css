section.related.products ul.products {
  display:flex;
  
}
li.product.type-product:not(.content-single-product) {
  border-radius: var(--border-radius-sm);
  background-color: var(--white);
  padding: 28px 16px !important;
  /* margin-right: 16px !important;
  margin-bottom: 12px !important; */
  margin:0 !important;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: 100%;
  width: 100%!important;
  min-width: fit-content;
  /* flex: 0 0 auto; */


  img {
    aspect-ratio: 1 / 1;
    width: 90%;
    height: auto;
    margin:0 auto;
    max-height: 260px;
    object-fit: contain;
  }
  .woocommerce-loop-product__title {
    color: var(--black);
    font-size: 18px;
    margin-bottom: 10px !important;
    overflow-y: auto;
    border-bottom: 1px solid var(--black);
  }
  .related-products-item-information {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    .price {
      font-size: 22px;
      margin:0;
      white-space: nowrap;
      color: var(--secondary-color) !important;
    }
    .stock-status {
      color: var(--black);
      font-size: 12px;
      white-space: nowrap;
    }
  }
  .related-products-item-add-to-cart {
    display:flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 16px;
    p,a {
      margin:0;
    }
    p {
      margin-left: 8px;
    }
    .amount-fp {
      font-size: 10px;
      color: var(--black);
      white-space: nowrap;
    }
    .button {
      background-color: var(--secondary-color);
      border: 1px solid var(--secondary-color);
      font-size: 12px;
      border-radius: var(--border-radius);
      padding:4px;
      padding-left: 12px;
      color: var(--white);
      display: flex;
      gap: 2rem;
      align-items: center;
      text-wrap: nowrap;
      max-width: min-content;
      text-decoration: none;
      cursor:pointer;
      &::after {
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-arrow-right'%3E%3Cpath d='M5 12h14M12 5l7 7-7 7'/%3E%3C/svg%3E");
          display:flex;
          justify-content: center;
          align-items: center;
          /* Icon styling */
          width: 32px;
          height: 32px;
          /* White background */
          background-color: white;
          border-radius: 50px;
      }
      div {
          background-color: var(--white);
          padding: 0.5rem 0.5rem 6px 0.5rem;
          border-radius: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          
      }
    }
  }
}