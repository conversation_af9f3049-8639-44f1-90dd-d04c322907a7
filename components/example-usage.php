<?php
/**
 * Example usage of the React-like component system
 * 
 * This file demonstrates how to use the component system with various props
 */

// Include the component system
require_once 'index.php';

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component System Examples</title>
    <style>
        /* Basic button styles for demonstration */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-family: Arial, sans-serif;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 4px;
        }
        
        .btn-primary { background-color: #007bff; color: white; }
        .btn-primary:hover { background-color: #0056b3; }
        
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-secondary:hover { background-color: #545b62; }
        
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-danger:hover { background-color: #c82333; }
        
        .btn-outline { background-color: transparent; color: #007bff; border: 1px solid #007bff; }
        .btn-outline:hover { background-color: #007bff; color: white; }
        
        .btn-small { padding: 4px 8px; font-size: 12px; }
        .btn-medium { padding: 8px 16px; font-size: 14px; }
        .btn-large { padding: 12px 24px; font-size: 16px; }
        
        .btn-disabled { opacity: 0.6; cursor: not-allowed; }
        .btn-disabled:hover { background-color: inherit; }
        
        .example-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .example-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .code-example {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>React-like PHP Component System Examples</h1>
    
    <div class="example-section">
        <h3>Basic Button Usage</h3>
        <div class="code-example">
            &lt;?php get_component('button'); ?&gt;
        </div>
        <?php get_component('button'); ?>
    </div>
    
    <div class="example-section">
        <h3>Button with Custom Text</h3>
        <div class="code-example">
            &lt;?php get_component('button', ['text' => 'Save Changes']); ?&gt;
        </div>
        <?php get_component('button', ['text' => 'Save Changes']); ?>
    </div>
    
    <div class="example-section">
        <h3>Different Button Types</h3>
        <div class="code-example">
            &lt;?php get_component('button', ['text' => 'Primary', 'type' => 'primary']); ?&gt;<br>
            &lt;?php get_component('button', ['text' => 'Secondary', 'type' => 'secondary']); ?&gt;<br>
            &lt;?php get_component('button', ['text' => 'Danger', 'type' => 'danger']); ?&gt;<br>
            &lt;?php get_component('button', ['text' => 'Outline', 'type' => 'outline']); ?&gt;
        </div>
        <?php get_component('button', ['text' => 'Primary', 'type' => 'primary']); ?>
        <?php get_component('button', ['text' => 'Secondary', 'type' => 'secondary']); ?>
        <?php get_component('button', ['text' => 'Danger', 'type' => 'danger']); ?>
        <?php get_component('button', ['text' => 'Outline', 'type' => 'outline']); ?>
    </div>
    
    <div class="example-section">
        <h3>Different Button Sizes</h3>
        <div class="code-example">
            &lt;?php get_component('button', ['text' => 'Small', 'size' => 'small']); ?&gt;<br>
            &lt;?php get_component('button', ['text' => 'Medium', 'size' => 'medium']); ?&gt;<br>
            &lt;?php get_component('button', ['text' => 'Large', 'size' => 'large']); ?&gt;
        </div>
        <?php get_component('button', ['text' => 'Small', 'size' => 'small']); ?>
        <?php get_component('button', ['text' => 'Medium', 'size' => 'medium']); ?>
        <?php get_component('button', ['text' => 'Large', 'size' => 'large']); ?>
    </div>
    
    <div class="example-section">
        <h3>Button as Link</h3>
        <div class="code-example">
            &lt;?php get_component('button', [<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'text' => 'Visit Google',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'href' => 'https://google.com',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'target' => '_blank'<br>
            ]); ?&gt;
        </div>
        <?php get_component('button', [
            'text' => 'Visit Google',
            'href' => 'https://google.com',
            'target' => '_blank'
        ]); ?>
    </div>
    
    <div class="example-section">
        <h3>Disabled Button</h3>
        <div class="code-example">
            &lt;?php get_component('button', ['text' => 'Disabled', 'disabled' => true]); ?&gt;
        </div>
        <?php get_component('button', ['text' => 'Disabled', 'disabled' => true]); ?>
    </div>
    
    <div class="example-section">
        <h3>Button with JavaScript</h3>
        <div class="code-example">
            &lt;?php get_component('button', [<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'text' => 'Click Me',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'onclick' => 'alert("Hello from component!")'<br>
            ]); ?&gt;
        </div>
        <?php get_component('button', [
            'text' => 'Click Me',
            'onclick' => 'alert("Hello from component!")'
        ]); ?>
    </div>
    
    <div class="example-section">
        <h3>Button with Icon</h3>
        <div class="code-example">
            &lt;?php get_component('button', [<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'text' => 'Download',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'icon' => '⬇️',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'icon_position' => 'left'<br>
            ]); ?&gt;
        </div>
        <?php get_component('button', [
            'text' => 'Download',
            'icon' => '⬇️',
            'icon_position' => 'left'
        ]); ?>
        <?php get_component('button', [
            'text' => 'Upload',
            'icon' => '⬆️',
            'icon_position' => 'right'
        ]); ?>
    </div>
    
    <div class="example-section">
        <h3>Using render_component() to Get HTML String</h3>
        <div class="code-example">
            &lt;?php<br>
            $button_html = render_component('button', ['text' => 'Generated']);<br>
            echo "Button HTML: " . htmlspecialchars($button_html);<br>
            ?&gt;
        </div>
        <?php
        $button_html = render_component('button', ['text' => 'Generated']);
        echo "Button HTML: " . htmlspecialchars($button_html);
        ?>
    </div>
    
    <div class="example-section">
        <h3>Complex Example</h3>
        <div class="code-example">
            &lt;?php get_component('button', [<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'text' => 'Submit Form',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'type' => 'primary',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'size' => 'large',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'id' => 'submit-btn',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'class' => 'my-custom-class',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'onclick' => 'submitForm()',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;'icon' => '✓'<br>
            ]); ?&gt;
        </div>
        <?php get_component('button', [
            'text' => 'Submit Form',
            'type' => 'primary',
            'size' => 'large',
            'id' => 'submit-btn',
            'class' => 'my-custom-class',
            'onclick' => 'submitForm()',
            'icon' => '✓'
        ]); ?>
    </div>
    
    <script>
        function submitForm() {
            alert('Form would be submitted!');
        }
    </script>
</body>
</html>
