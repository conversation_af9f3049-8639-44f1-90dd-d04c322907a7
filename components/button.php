<?php

/**
 * Button Component
 *
 * Available props:
 * - text: Button text content (default: 'Click me')
 * - type: Button type - 'primary', 'secondary', 'danger', 'outline' (default: 'primary')
 * - size: Button size - 'small', 'medium', 'large' (default: 'medium')
 * - href: If provided, renders as a link instead of button
 * - target: Link target (default: '_self')
 * - disabled: Whether button is disabled (default: false)
 * - class: Additional CSS classes
 * - id: Button ID attribute
 * - onclick: JavaScript onclick handler
 * - icon: Icon class or HTML (optional)
 * - icon_position: 'left' or 'right' (default: 'left')
 */

// Set default values for props
$text = get_prop('text', 'Click me');
$type = get_prop('type', 'primary');
$size = get_prop('size', 'medium');
$href = get_prop('href', '');
$target = get_prop('target', '_self');
$disabled = get_prop('disabled', false);
$class = get_prop('class', '');
$id = get_prop('id', '');
$onclick = get_prop('onclick', '');
$icon = get_prop('icon', '');
$icon_position = get_prop('icon_position', 'left');

// Build CSS classes
$css_classes = ['btn', "btn-{$type}", "btn-{$size}"];

if ($disabled) {
      $css_classes[] = 'btn-disabled';
}

if (!empty($class)) {
      $css_classes[] = $class;
}

$class_string = implode(' ', $css_classes);

// Build attributes
$attributes = [];

if (!empty($id)) {
      $attributes[] = "id=\"{$id}\"";
}

if (!empty($onclick)) {
      $attributes[] = "onclick=\"{$onclick}\"";
}

if ($disabled) {
      $attributes[] = 'disabled';
}

$attributes_string = implode(' ', $attributes);

// Prepare icon HTML
$icon_html = '';
if (!empty($icon)) {
      // Check if icon contains HTML tags or is just a class
      if (strpos($icon, '<') !== false) {
            $icon_html = $icon;
      } else {
            $icon_html = "<i class=\"{$icon}\"></i>";
      }
}

// Render the component
if (!empty($href)) {
      // Render as link
      echo "<a href=\"{$href}\" target=\"{$target}\" class=\"{$class_string}\" {$attributes_string}>";

      if (!empty($icon) && $icon_position === 'left') {
            echo $icon_html . ' ';
      }

      echo $text;

      if (!empty($icon) && $icon_position === 'right') {
            echo ' ' . $icon_html;
      }

      echo "</a>";
} else {
      // Render as button
      echo "<button class=\"{$class_string}\" {$attributes_string}>";

      if (!empty($icon) && $icon_position === 'left') {
            echo $icon_html . ' ';
      }

      echo $text;

      if (!empty($icon) && $icon_position === 'right') {
            echo ' ' . $icon_html;
      }

      echo "</button>";
}
