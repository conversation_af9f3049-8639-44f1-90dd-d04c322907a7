.btn { 
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; 
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: 1px solid transparent;
  transition: filter 0.2s ease;
}
.btn-small {
  padding: 0.5rem 1rem;  
}
.btn-medium {
  padding: 0.75rem 1.5rem;
}
.btn-large {
  padding: 1rem 2rem;
}
.btn-primary {
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: var(--white);
}
.btn-primary:hover {
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    filter: brightness(1.2);
    color: var(--white);
}
.btn-secondary {
    background-color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    color: var(--white);
}
.btn-secondary:hover {
    background-color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    color: var(--white);
}
.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
