<?php

/**
 * Button Component
 * 
 * Available props:
 * @param string $text
 * @param string $type
 * @param string $size
 * @param string $href
 * @param bool $disabled
 * @param string $class 
 * @param string $id
 * @param string $onclick
 * @param string $icon
 * @param string $icon_position
 * @param string $target
 * 
 */

// Set default values for props

$text = get_prop('text', 'Läs mer');
$type = get_prop('type', 'primary');
$size = get_prop('size', 'medium');
$href = get_prop('href', '');
$target = get_prop('target', '_self');
$disabled = get_prop('disabled', false);
$class = get_prop('class', '');
$id = get_prop('id', '');
$onclick = get_prop('onclick', '');
$icon = get_prop('icon', '');
$icon_position = get_prop('icon_position', 'left');

// Build CSS classes
$css_classes = ['btn', "btn-{$type}", "btn-{$size}"];

// Get button.css styling
wp_enqueue_style('button-style', get_stylesheet_directory_uri() . '/components/button/button.css', array(), '1.0', 'all');

if ($disabled) {
      $css_classes[] = 'btn-disabled';
}

if (!empty($class)) {
      $css_classes[] = $class;
}

$class_string = implode(' ', $css_classes);

// Build attributes
$attributes = [];

if (!empty($id)) {
      $attributes[] = "id=\"{$id}\"";
}

if (!empty($onclick)) {
      $attributes[] = "onclick=\"{$onclick}\"";
}

if ($disabled) {
      $attributes[] = 'disabled';
}

$attributes_string = implode(' ', $attributes);

// Prepare icon HTML
$icon_html = '';
if (!empty($icon)) {
      // Check if icon contains HTML tags or is just a class
      if (strpos($icon, '<') !== false) {
            $icon_html = $icon;
      } else {
            $icon_html = "<i class=\"{$icon}\"></i>";
      }
}

// Render the component
if (!empty($href)) {
      // Render as link
      echo "<a href=\"{$href}\" target=\"{$target}\" class=\"{$class_string}\" {$attributes_string}>";

      if (!empty($icon) && $icon_position === 'left') {
            echo $icon_html . ' ';
      }

      echo $text;

      if (!empty($icon) && $icon_position === 'right') {
            echo ' ' . $icon_html;
      }

      echo "</a>";
} else {
      // Render as button
      echo "<button class=\"{$class_string}\" {$attributes_string}>";

      if (!empty($icon) && $icon_position === 'left') {
            echo $icon_html . ' ';
      }

      echo $text;

      if (!empty($icon) && $icon_position === 'right') {
            echo ' ' . $icon_html;
      }

      echo "</button>";
}
