<?php

/**
 * Load and render a component with props (React-like functionality)
 *
 * @param string $component_name The name of the component file (without .php extension)
 * @param array $props Props/options to pass to the component
 * @param bool $return Whether to return the output instead of echoing it
 * @return string|void Returns the component output if $return is true
 */
function get_component($component_name, $props = [], $return = false)
{
  $component_file = get_stylesheet_directory() . '/components/' . $component_name . '/' . $component_name . '.php';

  if (!file_exists($component_file)) {
    $error_message = "Component '{$component_name}' not found at: {$component_file}";
    if ($return) {
      return $error_message;
    }
    echo $error_message;
    return;
  }

  // Start output buffering to capture component output
  ob_start();

  // Make props available globally for get_prop() function
  global $component_props;
  $component_props = $props;

  // Extract props as variables for use in the component
  if (!empty($props)) {
    extract($props, EXTR_SKIP); // EXTR_SKIP prevents overwriting existing variables
  }

  // Include the component file
  include $component_file;

  // Get the buffered content
  $output = ob_get_clean();

  if ($return) {
    return $output;
  }

  echo $output;
}

/**
 * Render a component and return its output as a string
 *
 * @param string $component_name The name of the component
 * @param array $props Props to pass to the component
 * @return string The rendered component output
 */
function render_component($component_name, $props = [])
{
  return get_component($component_name, $props, true);
}

/**
 * Helper function to safely get a prop value with a default
 * This function works within component files to access props safely
 *
 * @param string $key The prop key
 * @param mixed $default Default value if prop doesn't exist
 * @param array $props_array Props array (optional, will auto-detect from component scope)
 * @return mixed The prop value or default
 */
function get_prop($key, $default = '', $props_array = null)
{
  // If props array is explicitly provided, use it
  if ($props_array !== null) {
    return isset($props_array[$key]) ? $props_array[$key] : $default;
  }

  // Get all variables from the calling scope
  $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
  $caller_file = isset($trace[0]['file']) ? $trace[0]['file'] : '';

  // If we're being called from a component file, try to access $props
  if (strpos($caller_file, '/components/') !== false) {
    // Use a global variable to pass props to components
    global $component_props;
    if (isset($component_props) && is_array($component_props) && isset($component_props[$key])) {
      return $component_props[$key];
    }
  }

  return $default;
}
