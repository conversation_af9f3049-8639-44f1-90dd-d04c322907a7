<?php

/**
 * Load and render a component with props (React-like functionality)
 *
 * @param string $component_name The name of the component file (without .php extension)
 * @param array $props Props/options to pass to the component
 * @param bool $return Whether to return the output instead of echoing it
 * @return string|void Returns the component output if $return is true
 */
function get_component($component_name, $props = [], $return = false)
{
  $component_file = get_stylesheet_directory() . '/components/' . $component_name . '.php';

  if (!file_exists($component_file)) {
    $error_message = "Component '{$component_name}' not found at: {$component_file}";
    if ($return) {
      return $error_message;
    }
    echo $error_message;
    return;
  }

  // Start output buffering to capture component output
  ob_start();

  // Extract props as variables for use in the component
  if (!empty($props)) {
    extract($props, EXTR_SKIP); // EXTR_SKIP prevents overwriting existing variables
  }

  // Include the component file
  include $component_file;

  // Get the buffered content
  $output = ob_get_clean();

  if ($return) {
    return $output;
  }

  echo $output;
}

/**
 * Render a component and return its output as a string
 *
 * @param string $component_name The name of the component
 * @param array $props Props to pass to the component
 * @return string The rendered component output
 */
function render_component($component_name, $props = [])
{
  return get_component($component_name, $props, true);
}

/**
 * Helper function to safely get a prop value with a default
 *
 * @param string $key The prop key
 * @param mixed $default Default value if prop doesn't exist
 * @param array $props Props array (optional, uses extracted variables by default)
 * @return mixed The prop value or default
 */
function get_prop($key, $default = '', $props = null)
{
  if ($props !== null) {
    return isset($props[$key]) ? $props[$key] : $default;
  }

  // If no props array provided, check if variable exists in current scope
  return isset($$key) ? $$key : $default;
}
