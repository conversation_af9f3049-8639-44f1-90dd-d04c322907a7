body, html {
    margin: 0;
    padding: 0;
}

header.smort-header {
  display: flex; 
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  margin: 0;

  .header-top-wrapper {
    background-color: var(--white);
  }
  
  .header-top{
    margin-inline: var(--side-margin);
    display: flex;
    justify-content: space-between;
    padding-block: 0.875rem;
    background-color: var(--white);
    div:has(.swcs-search-trigger) {
      width: 100%;
      max-width: 700px;
      margin-inline: 0.5rem;
      .swcs-search-trigger {
        border: unset;
        border-radius: var(--border-radius);
        background-color: var(--accentcolor3);
        display:flex;
        align-items: center;
        justify-content: space-between;
        &::after {
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
          background-color: var(--secondary-color);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding:8px 8px 3px 8px;
        }
      }
    }
  }

  .header-bottom {
    width: 100%;
    background-color: var(--accentcolor3);
    border-bottom: 1px solid var(--accentcolor2);
    padding-block: 8px;
    .header-bottom-inner {
      margin: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .main-navigation {
      .menu-item a {
        white-space: nowrap;
        .menu-down-icon {
          filter: invert(13%) sepia(88%) saturate(6977%) hue-rotate(343deg) brightness(89%) contrast(115%);
        }
      }
    }

    .header-megamenu-column {
      max-width: 16.6%;

    }
    .header-megamenu-heading {

    }
  }
  .smort-logo img {
    display: block;
    margin: 0 auto;
    width: auto;
    min-width:100%;
    max-height: 50px;
    object-fit: contain;
  }
  
  .smort-nav {
    text-align: center;
  }
  
  .smort-nav.smort-nav-centered {
    width: 60%;
    justify-content: center;
  }
  
  .smort-nav.smort-nav-left {
    width: 90%;
    justify-content: flex-start;
  }
  
  .smort-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
  }
  
  .smort-nav ul li {
    margin: 0 15px;
  }
  
  .smort-nav ul li a {
    text-decoration: none;
    color: #000;
  }
  
  .smort-nav ul li a:hover,
  .smort-nav ul li a:active {
    color: var(--accentColor) !important;
  }
  
  .smort-cart {
    width: 25%;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  
  .smort-header-style_2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .smort-header-style_2 .smort-nav {
    width: 40%;
    text-align: left;
  }
  
  .smort-header-style_2 .smort-logo {
    width: 20%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }
  
  .smort-header-style_2 .smort-cart {
    width: 40%;
    text-align: right;
  }
  
  .smort-header.transparent {
    background: none;
    position: absolute;
    width: 100%;
  }
}

.custom-megamenu-content {
  .header-megamenu-heading {
    padding-bottom: 24px;
    border-bottom: 1px solid var(--secondary-color);
  }
  .header-megamenu-heading:empty {
    padding-bottom: 40px;
    border-bottom: 1px solid var(--secondary-color);
  }
  }
  :where(.wp-block-columns.is-layout-flex) {
    gap:0;
  }
  .header-megamenu-column-extended, .header-megamenu-column {
    text-align: left;
  }
  .header-megamenu-column-extended, .header-megamenu-column:not(:last-of-type) {
    margin-right: 24px !important;
  }

 .header-megamenu-column:has(+ .header-megamenu-column-extended) {
    margin-right: 0px !important;
  }

body.transparent-header {
  margin-top: 0;
}
/* Add this to style.css */

/* Burger Menu */
.burger-menu {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  width: 30%;
}

.burger-menu span {
  background: #000;
  height: 2px;
  width: 100%;
  margin: 4px 0;
}
.sub-menu-xk {
  display: flex;
  flex-direction: row;
  width: 100%;
  width: 90%;
  overflow-y: auto;
  text-align: left;
  padding-bottom: 10%;
  border-top: 1px solid #f1f1f1;
  padding-top: 5%;
  gap: 40px;
  text-transform: uppercase;
  font-family: 'CustomHeadingFont';
}
.sub-menu-xk a {
    color:#fff;
    font-size: 2rem;
}

.mobile-nav-inner {
    text-align: center;
    width: 100%;
}

.close-nav {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 30px;
    color: #fff;
    cursor: pointer;
}

@media (min-width: 992px) {
.burger-menu-container{
  display: none !important;
}
}

@media (max-width: 992px) {
    .burger-menu {
        display: flex;
    }
    
    .main-navigation {
        display: none;
    }
}
