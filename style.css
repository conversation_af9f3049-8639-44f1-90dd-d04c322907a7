/* 
Theme Name: Promotion AB(Test) x Smort Commerce
Theme URI: http://example.com/smort
Author: Smort AB
Author URI: http://example.com
Description: Promotion AB (Test) Tema
Version: 1.0
License: GNU General Public License v2 eller senare
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: smort
*/

:root {
    --primary-color: #162a49;
    --secondary-color: #E4002B;
    --black: #212121;
    --white: #ffffff; 
    --accentcolor: #FEFFF8; /* Default accent color */
    --accentcolor2: #707070;
    --accentcolor3: #F4F4F4;
    --fontFamily: 'Montserrat, sans-serif'; /* Default font family */
    
    --border-radius: 100px; /* Default border radius */
    --border-radius-sm: 0.4rem; /* Default border radius */
    --border-radius-md: 4rem; /* Default border radius */
    --delay: 0.25s;

    --side-margin: 32px;
}
@media (width <= 1280px) {
    :root {
        --side-margin: 32px;
    }
}
@media (width <= 768px) {
    :root {
        --side-margin: 24px;
    }
}
@media (width <= 480px) {
    :root {
        --side-margin: 16px;
    }
}

* {
    box-sizing:border-box;
    interpolate-size: allow-keywords;
}

/* Remove Admin Bar when logged in */
html:has(body.logged-in) {
    margin-top: 0 !important;
}
body.logged-in {
    #wpadminbar {
        display: none;
    }
}
body {
    overflow-x: hidden;
}


.wooj-icon-basket-1:before {
    content: url(/wp-content/themes/smort_commerce/assets/smort-shopping-icon.svg) !important;
    filter: invert(0); 
}
/* Mobile Menu Styles */
.mobile-menu-container {
    display: none; /* Hidden by default */
    background-color: #333;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    overflow: hidden;
    transition: transform 0.3s ease-in-out;
    transform: translateX(-100%);
}

.mobile-menu-container.active {
    transform: translateX(0);
}

.wrapper {
    margin-inline: 32px;
    @media (max-width: 1024px) {
        margin-inline: 24px;
    }
    @media (max-width: 480px) {
        margin-inline: 16px;
    }
}

.burger-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 1001;
    cursor: pointer;
}

.burger-icon {
    font-size: 30px;
    color: #fff;
}

.mobile-nav {
    margin-top: 50px;
}

.mobile-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: center;
}

.mobile-menu li {
    margin: 15px 0;
}

.mobile-menu li a {
    color: #fff;
    text-decoration: none;
    font-size: 20px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .desktop-menu {
        display: none; /* Assuming your desktop menu has a class 'desktop-menu' */
    }
    .mobile-menu-container {
        display: block;
    }
}


.wc-j-upsellator-cart-count-container{
    padding: 0px 5px !important;
}

/* Modal job */ 

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #000000;
    margin: auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 900px;
    text-align: center;
    position: relative;
}

.close-modal {
    color: #000;
    font-size: 24px;
    font-weight: bold;
    position: absolute;
    top: 10px;
    right: 20px;
    cursor: pointer;
}

.modal h2 {
    font-family: var(--fontFamily);
    color: var(--accentColor3);
    margin-bottom: 20px;
    font-size: 3rem;
}

.form-field label {
    color: #fff;
    font-family: 'CustomHeadingFont';
} 

.promotion-global-btn {
    background-color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    font-size: 0.875rem;
    border-radius: var(--border-radius);
    padding:4px;
    padding-left: 12px;
    color: var(--white);
    display: flex;
    gap: 2rem;
    align-items: center;
    text-wrap: nowrap;
    max-width: min-content;
    text-decoration: none;
    cursor:pointer;
    div {
        background-color: var(--white);
        padding: 0.5rem 0.5rem 6px 0.5rem;
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &::after {
            content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-arrow-right'%3E%3Cpath d='M5 12h14M12 5l7 7-7 7'/%3E%3C/svg%3E");
            display: inline-block;
            vertical-align: middle;
        }
    }

    &.size-large {
      padding:8px;
      padding-left: 16px;
            gap: 3rem;
    }

    &.variant-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    &.variant-accent {
        background-color: var(--white);
        border-color: var(--accentcolor2);
        color: var(--black);

        div {
            background-color: var(--primary-color);
            &::after {
                content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-arrow-right'%3E%3Cpath d='M5 12h14M12 5l7 7-7 7'/%3E%3C/svg%3E");
                display: inline-block;
                vertical-align: middle;
            }
            
        }
    }

    &:hover {
        filter: brightness(1.15);
    }
    &.customer-service {
        div {
            padding: 0.5rem 0.5rem 8px 0.5rem;
            &::after {
                content:'';
            }
        }
    }
} 

.stock-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.stock-green .stock-dot {
    background-color: green;
}

.stock-yellow .stock-dot {
    background-color: yellow;
}

.stock-red .stock-dot {
    background-color: red;
}

.woocommerce-breadcrumb {
    background-color: var(--primary-color);
    color: var(--white) !important;
    padding-block: 0.5rem !important;
    a, span {
        color: var(--accentcolor2) !important;
        text-decoration: none;
    }
}

.woocommerce ul.products {
    margin:0;
    padding: 0 0 1rem;
}