<?php

/**
 * ACF-f<PERSON>lt för <PERSON>t Hero Banner block
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_smort_hero_banner_fields',
        'title' => 'Smort Hero Banner Block Fields',
        'fields' => array(
            array(
                'key' => 'field_smort_hero_banner_text',
                'label' => 'Text',
                'name' => 'text',
                'type' => 'text',
                'instructions' => 'Enter the text for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_smort_hero_banner_textarea',
                'label' => 'Textarea',
                'name' => 'textarea',
                'type' => 'textarea',
                'instructions' => 'Enter the textarea for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter textarea...',
                'maxlength' => '',
                'rows' => 4,
                'new_lines' => 'wpautop',
            ),
            array(
                'key' => 'field_smort_hero_banner_image',
                'label' => 'Image',
                'name' => 'image',
                'type' => 'image',
                'instructions' => 'Enter the image for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
            ),
            array(
                'key' => 'field_smort_hero_banner_url',
                'label' => 'Url',
                'name' => 'url',
                'type' => 'url',
                'instructions' => 'Enter the url for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'https://example.com',
                'prepend' => '',
                'append' => '',
            )
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/smort-hero-banner',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => 'Fields for the Smort Hero Banner block',
    ));
}
