.category-carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding-bottom: 20px;
  .swiper-container-category{
    width: 100%;
  }

  .swiper-wrapper {
    display: flex;
    transition: transform 0.3s ease;
    align-items: flex-end;
    
    .swiper-slide.category-item {
      flex-shrink: 0;
      width: 100%;
      position: relative;
      overflow: hidden;
      text-decoration: none;
      display: flex;
      align-items: center;
      justify-content:center;
      flex-direction: column;
      max-width: 220px;
      padding-block:20px;
    }
    
    .overlay {
      position: absolute;
      background-color: transparent;
      height: 220px;
      width: 220px;
      border-radius: 100%;
      transition: background-color 0.2s ease-in-out;
    }

    img {
      width: 120px;
      max-height: 120px;
      padding-top: 20px;
      transition: filter 0.2s ease-in-out;
    }
    
    .category-info {
      position: relative;
      z-index: 2;
      max-width: 90%;
      margin: 0;
      display: flex;
      flex-direction: column;
      height: 50%;
      justify-content: space-between;
      align-items: flex-start;
      text-align: center;

      .category-title {
        font-size: 1rem;
        margin-bottom: 20px;
        margin-top: 10px;
        color: var(--primary-color);
        transition: color 0.2s ease-in-out;
      } 

    }
  }
}

@media screen and (max-width: 992px) {
  .swiper-slide.category-item{
      height: 450px;
  }
  .kategori-text{
    font-size: 0.8rem;
  }
}

.swiper-slide.category-item:hover {
  .overlay {
    background-color: var(--primary-color);
  }
  img {
    filter: brightness(0) invert(1);
  }
  .category-title {
    color: var(--white);
  }
}