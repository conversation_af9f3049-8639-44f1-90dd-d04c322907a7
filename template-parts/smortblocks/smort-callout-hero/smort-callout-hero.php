<div class="wrapper">
  <?php
  if (have_rows('callouts')) : ?>
    <div class="callouts-hero-grid">
      <?php while (have_rows('callouts')) : the_row();
        $image = !empty(get_sub_field('image')) ? get_sub_field('image') : null;
        $title = !empty(get_sub_field('title')) ? get_sub_field('title') : null;
        $preamble = !empty(get_sub_field('preamble')) ? get_sub_field('preamble') : null;
        $link = !empty(get_sub_field('link')) ? get_sub_field('link') : null;
      ?>
        <?php if ($link) : ?>
          <a href="<?= esc_url($link['url']) ?>" class="callouts-hero-card">
            <img class="callout-hero-card-img" src="<?= $image ?>" />
            <div class="callouts-hero-card-info">
              <?php if ($title) : ?>
                <h4><?= esc_html($title) ?></h4>
              <?php endif ?>
              <?php if ($preamble) : ?>
                <p> <?= $preamble ?> </p>
              <?php endif ?>
              <?php if ($link['title']) : ?>
                <button class="promotion-global-btn variant-primary"><span><?= $link['title'] ?></span>
                  <div></div>
                </button>
              <?php else : ?>
                <div class="simple-arrow">

                </div>
              <?php endif ?>
            </div>
          </a>
        <?php endif ?>
      <?php endwhile ?>
    </div>
  <?php endif ?>
</div>