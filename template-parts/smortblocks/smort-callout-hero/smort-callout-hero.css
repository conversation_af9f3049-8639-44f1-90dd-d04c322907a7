.callouts-hero-grid {
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: repeat(2,minmax(240px,auto));
  column-gap: 24px;
  row-gap: 24px;

  @media (max-width: 992px) {
    display: flex;
    flex-direction: column;
  }
  
  .callouts-hero-card {
    position: relative;
    overflow: hidden;
    border-radius: 2rem;
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: scale 0.2s ease-in-out;
    }
    &:hover img {
      scale: 1.05;
    }
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background-color: rgba(0,0,0,0.18);
      z-index: 1;
    }
    .callouts-hero-card-info {
      position: absolute;
      bottom: 16px;
      left: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      z-index: 2;
      color: var(--white);
     
      h4 {
        font-size: 3rem;
        margin-bottom: 20px;
        text-wrap: balance;
      }
      p {
        font-size: 1rem;
        margin-top: 0px;
        margin-bottom: 20px;
      }
      img {
        filter: brightness(0) invert(0);
        scale: 0.75;
      }
      .simple-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--white);
        border-radius: 4rem;
        padding: 0.5rem 0.5rem 4px 0.5rem;
        &::after {
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-arrow-right'%3E%3Cpath d='M5 12h14M12 5l7 7-7 7'/%3E%3C/svg%3E");
          display: inline-block;
          vertical-align: middle;
        }
        
      }
    }
  }
  .callouts-hero-card:first-child {
    grid-row: span 2;
  }

}