<?php

/**
 * Smort Product Slider Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during backend preview render.
 * @param   int $post_id The post ID the block is rendering content against.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'smort-product-slider-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'smort-product-slider';
if (!empty($block['className'])) {
    $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $className .= ' align' . $block['align'];
}

// Load values and assign defaults.
$title = get_field('title') ? get_field('title') : 'Våra produkter';
$button_text = get_field('button_text') ?: 'Till produkten';
$top_link = !empty(get_field('top_link')) ? get_field('top_link') : null;


// Hämta valda produkter
$selected_products = get_field('selected_products');

// Om inga produkter är valda, använd fallback till kategorifiltrering
if (empty($selected_products)) {
    $product_count = get_field('product_count') ?: 8;
    $product_categories = get_field('product_categories') ?: array();

    // Hämta produkter från WooCommerce
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => $product_count,
        'post_status'    => 'publish',
    );

    // Lägg till kategorifilter om det är valt
    if (!empty($product_categories)) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'term_id',
                'terms'    => $product_categories,
                'operator' => 'IN',
            ),
        );
    }

    $products_query = new WP_Query($args);
} else {
    // Använd de specifikt valda produkterna
    $args = array(
        'post_type'      => 'product',
        'post__in'       => $selected_products,
        'posts_per_page' => -1,
        'orderby'        => 'post__in', // Behåll ordningen från ACF-fältet
        'post_status'    => 'publish',
    );

    $products_query = new WP_Query($args);
}
?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
    <!-- Section title -->
    <div class="smort-product-slider-title-container wrapper">
        <?php if ($title) : ?>
            <h2> <?= esc_html($title) ?></h2>
        <?php endif ?>
        <?php if ($top_link) : ?>
            <a href="<?= $top_link['url'] ?>" class="promotion-global-btn variant-accent">
                <span><?= $top_link['title'] ?></span>
                <div> </div>
            </a>
        <?php endif ?>
    </div>
    <?php if ($products_query->have_posts()) : ?>
        <div class="smort-product-slider-container">
            <div class="smort-product-slider-track">
                <?php
                $count = 0;
                while ($products_query->have_posts()) : $products_query->the_post();
                    global $product;

                    // Kontrollera att det är en giltig WooCommerce-produkt
                    if (!is_a($product, 'WC_Product')) {
                        continue;
                    }

                    $count++;
                    $product_id = $product->get_id();
                    $product_name = $product->get_name();
                    $product_price = $product->get_price_html();
                    $product_link = get_permalink($product_id);
                    $product_image = wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'Full');
                    $product_image_url = $product_image ? $product_image[0] : wc_placeholder_img_src('Full');
                    $product_amount_fp = $product->get_attribute('antal-forpackning') ?? null;
                    // Kontrollera om produkten är i lager
                    $in_stock = $product->is_in_stock() ? 'I LAGER' : 'EJ I LAGER';
                    $in_stock_class = $product->is_in_stock() ? 'stock-green' : 'stock-red';
                    $stock_class = $product->is_in_stock() ? 'in-stock' : 'out-of-stock';
                ?>
                    <div class="smort-product-item" data-index="<?php echo $count; ?>">
                        <a href="<?php echo esc_url($product_link); ?>" class="smort-product-link">
                            <div class="smort-product-image-container">
                                <img src="<?php echo esc_url($product_image_url); ?>" alt="<?php echo esc_attr($product_name); ?>" class="smort-product-image">
                            </div>
                        </a>
                        <div class="product-title-container">
                            <h3 class="smort-product-name"><?php echo esc_html($product_name); ?></h3>
                        </div>

                        <div class="smort-product-info">
                            <div class="smort-product-price-stock">
                                <div class="smort-product-price"><?php echo $product_price; ?></div>

                                <?php
                                global $product;
                                // Hämta lagerstatus

                                $stock_status = $product->get_stock_status();
                                $backorders = $product->backorders_allowed();
                                $stock_text = '';
                                $stock_class = '';

                                if ($stock_status === 'instock' && !$backorders) {
                                    $stock_text = 'Leverans 1-2 dagar';
                                    $stock_class = 'stock-green';
                                } elseif ($stock_status === 'onbackorder') {
                                    $stock_text = 'Leverans 6-10 dagar';
                                    $stock_class = 'stock-yellow';
                                } else {
                                    $stock_text = 'Slut i lager';
                                    $stock_class = 'stock-red';
                                }

                                ?>
                                <div class="stock-status <?php echo esc_attr($stock_class); ?>">
                                    <span class="stock-dot"></span> <?php echo esc_html($stock_text); ?>
                                </div>
                            </div>
                        </div>

                        <div class="smort-product-button-container">
                            <a href="<?php echo esc_url($product_link); ?>" class="promotion-global-btn"><?php echo esc_html($button_text); ?>
                                <div>
                                </div>
                            </a>
                            <?php if ($product_amount_fp) : ?>
                                <p class="smort-product-slider-amount-fp">
                                    <?= 'Antal/fp ' . esc_html($product_amount_fp) ?>
                                </p>
                            <?php endif ?>
                        </div>
                    </div>
                <?php endwhile; ?>
                <?php wp_reset_postdata(); ?>
            </div>

            <!-- Progress bar -->
            <!-- <div class="smort-product-progress-container">
                <div class="smort-product-progress-bar"></div>
            </div> -->
        </div>

        <!-- Navigation arrows -->
        <div class="smort-product-nav wrapper">
            <button class="smort-product-nav-prev" aria-label="Föregående">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right" style="transform: rotate(180deg);">
                    <path d="M5 12h14M12 5l7 7-7 7" />
                </svg>
            </button>
            <button class="smort-product-nav-next" aria-label="Nästa">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right">
                    <path d="M5 12h14M12 5l7 7-7 7" />
                </svg>
            </button>
        </div>
    <?php elseif (is_admin()) : ?>
        <div class="smort-product-slider-placeholder">
            <p><?php _e('Inga produkter hittades. Välj specifika produkter eller ändra filterinställningarna.', 'smort'); ?></p>
        </div>
    <?php endif; ?>
</div>