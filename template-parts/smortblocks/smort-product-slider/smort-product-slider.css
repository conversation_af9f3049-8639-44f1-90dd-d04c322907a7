/* Smort Product Slider Styles - Uppdaterad enligt referensbild */

.smort-product-slider {
  position: relative;
  width: 100%;
  margin: 40px 0;
  padding: 0;
  background-color: transparent;
  overflow: visible; /* Säkerställer att pilarna syns även om de går utanför */
}

.smort-product-slider-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 30px;
  text-transform: uppercase;
}

/* Slider container */
.smort-product-slider-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding-bottom: 15px; /* Utrymme för progress bar */
}

/* Navigation arrows */
.smort-product-nav {
  position: relative;
  display: flex;
  gap: 10px;
  z-index: 20;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.smort-product-nav-prev,
.smort-product-nav-next {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: transparent;
  border: 1px solid var(--primary-color);
  background-color: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #000;
  padding: 0;
}

.smort-product-nav-prev:hover,
.smort-product-nav-next:hover {
  background-color: #f5f5f5;
  color: #000;
  svg {
    stroke: #000
  }
}

.smort-product-nav-prev:focus,
.smort-product-nav-next:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Slider track */
.smort-product-slider-track {
  display: flex;
  flex-wrap: nowrap;
  gap: 1.5rem;
  width: 100%;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  cursor: grab;
  padding-bottom: 10px;
  position: relative;
}

.smort-product-slider-track:active {
  cursor: grabbing;
}

/* Dölj scrollbar */
.smort-product-slider-track::-webkit-scrollbar {
  display: none;
}

/* Produkt-item */
.smort-product-item {
  flex: 0 0 calc(25% - 7.5px); /* 4 produkter i desktop med 10px gap */
  background-color: transparent;
  border: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  scroll-snap-align: start;
  min-width: 0; /* Förhindrar att innehållet expanderar utanför containern */


}

.smort-product-link {
  display: block;
  width: 100%;
  text-decoration: none;
  cursor: pointer;
  z-index: 1;
}

/* Produkt-bild container */
.smort-product-image-container {
  position: relative;
  width: 100%;
  padding-bottom: 100%;
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 10px;
}

/* Produkt-bild */
.smort-product-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ändrat till cover enligt önskemål */
  padding: 0;
  transition: transform 0.3s ease;
  cursor: pointer;
}

/* Knapp-container */
/* .smort-product-button-container {
  margin-top: 10px;
  opacity: 0;
  height: 40px; 
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
} */

/* Visa knapp vid hover */
.smort-product-item:hover .smort-product-button-container {
  opacity: 1;
  visibility: visible;
}

/* Knapp */
.smort-product-button {
  display: inline-block;
  padding: 8px 15px;
  background-color: #000;
  color: #fff;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.smort-product-button:hover {
  background-color: #333;
}

/* Produkt-info */
.smort-product-info {
  padding: 15px 0 5px 0;
  display: flex;
  flex-direction: column;
}

/* Produktnamn */
.smort-product-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 5px 0;
  text-transform: uppercase;
  color: #000;
}

/* Pris och lager-container */
.smort-product-price-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Pris */
.smort-product-price {
  font-size: 14px;
  font-weight: 400;
  color: #000;
}

/* Lager-status */
.smort-product-stock {
  font-size: 14px;
  text-transform: uppercase;
  text-align: right;
  color: #000;
}

.smort-product-stock.in-stock {
  color: #000;
}

.smort-product-stock.out-of-stock {
  color: #999;
}

/* Progress bar */
.smort-product-progress-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #fff; /* Vit bakgrund enligt önskemål */
  z-index: 10;
}

.smort-product-progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: #000;
  transition: width 0.3s ease;
}

/* Admin placeholder */
.smort-product-slider-placeholder {
  padding: 40px;
  background: #f0f0f0;
  text-align: center;
  border: 1px dashed #ccc;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .smort-product-item {
    flex: 0 0 calc(33.333% - 7px); /* 3 produkter i mindre desktop */
  }
}

@media (max-width: 768px) {
  .smort-product-slider-track {
    gap: 10px;
  }

  .smort-product-item {
    flex: 0 0 calc(33.333% - 7px); /* 3 produkter i tablet */
    height: auto;
    scroll-snap-align: start;
  }

  .smort-product-nav {
    margin-bottom: 10px;
  }

  .smort-product-nav-prev,
  .smort-product-nav-next {
    width: 28px;
    height: 28px;
  }

  .smort-product-link {
    display: block;
    width: 100%;
    text-decoration: none;
    pointer-events: auto;
  }

  .smort-product-slider-title {
    font-size: 28px;
    margin-bottom: 20px;
  }

  .smort-product-image-container {
    padding-bottom: 100%;
    height: 0;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .smort-product-slider-track {
    gap: 10px;
    width: 100%;
    overflow-x: auto;
    padding-bottom: 10px;
    scroll-snap-type: x mandatory;
  }

  .smort-product-item {
    flex: 0 0 calc(50% - 5px); /* 2 produkter i mobil */
    height: auto;
    margin: 0;
    display: flex;
    flex-direction: column;
    scroll-snap-align: start;
  }

  .smort-product-nav {
    margin-bottom: 8px;
  }

  .smort-product-nav-prev,
  .smort-product-nav-next {
    width: 24px;
    height: 24px;
  }

  .smort-product-nav-prev svg,
  .smort-product-nav-next svg {
    width: 14px;
    height: 14px;
  }

  .smort-product-link {
    display: block;
    width: 100%;
    text-decoration: none;
    pointer-events: auto;
  }

  .smort-product-name {
    font-size: 14px;
  }

  .smort-product-price {
    font-size: 11px;
  }

  .smort-product-stock {
    font-size: 9px;
  }

  .smort-product-button {
    padding: 6px 12px;
    font-size: 14px;
  }

  .smort-product-slider-title {
    font-size: 24px;
    margin-bottom: 15px;
  }

  .smort-product-image-container {
    padding-bottom: 100%;
    height: 0;
    width: 100%;
    margin-bottom: 10px;
  }

  /* Behåll overflow för att kunna scrolla */
  .smort-product-slider-container {
    overflow: hidden;
    padding-bottom: 30px;
  }

  .smort-product-progress-container {
    bottom: 0;
  }
}


.smort-product-slider-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--black);
  margin-bottom: 1rem;
}


.smort-product-slider-container {
  .smort-product-link {
    display: flex;
    justify-content: center;

  }
  .smort-product-item {
    .smort-product-image-container {
      display: flex;
      justify-content: center;
      height: auto;
      img {
        object-fit: contain;
      }    
    }
    border-radius: var(--border-radius-sm);
    padding:1rem 0.5rem;
    &:first-child {
      padding-left: 1.5rem;
    }
    &:last-child {
      padding-right: 1.5rem;
    }
    .product-title-container {
      padding-bottom: 0.5rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      .smort-product-name {
        font-size: 1.125rem;
        font-weight: 600;
      }
    }

    .smort-product-info {
      padding-bottom: 1.25rem;
      .smort-product-price{
        color: var(--secondary-color);
        font-weight: 700;
        font-size: 1.25rem;
      }
    }
  }
  .smort-product-button-container {
    display: flex;
    justify-content: flex-start;
    gap: 2rem;

    .smort-product-slider-amount-fp {
      /* font-weight: 700; */
    }
  }
}