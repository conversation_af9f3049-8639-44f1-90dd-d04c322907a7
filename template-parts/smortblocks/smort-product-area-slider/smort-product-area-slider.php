<?php

// Create id attribute allowfing for custom "anchor" value
$id = !empty($block['anchor']) ? $block['anchor'] : 'smort-product-areas' . '_' . $block['id'];

// Set a class name prefix 
$prefix = 'smort-product-areas';

// Create class attribute for custom "className" and "align" values
$className = $prefix;
$className .= !empty($block['className']) && ' ' . $block['className'];
$className .= !empty($block['align']) && ' align' . $block['align'];


// Load fields from the Block
$title = !empty(get_field('title')) ? get_field('title') : null;
$top_link = !empty(get_field('top_link')) ? get_field('top_link') : null;
$selected_product_areas = !empty(get_field('selected_product_areas')) ? get_field('selected_product_areas') : null;




// Get the taxonomy data
$product_areas = get_terms(array(
  'taxonomy' => 'produktomrade',
  'include' => $selected_product_areas
));

?>

<section id="<?php echo esc_attr($id) ?>" class="<?= esc_attr($className); ?>">
  <!-- Section Title -->
  <div class="<?= $prefix . '-title-container wrapper' ?>">
    <?php if ($title) : ?>
      <h2><?= esc_html($title) ?></h2>
    <?php endif; ?>
    <?php if ($top_link) : ?>
      <a href="<?= $top_link['url'] ?>" class="promotion-global-btn variant-accent">
        <span><?= $top_link['title'] ?></span>
        <div></div>
      </a>
    <?php endif; ?>
  </div>
  <!-- Product Areas Slider -->
  <div class="<?= $prefix . '-container' ?>">
    <div class="<?= $prefix . '-track' ?>">

      <?php foreach ($product_areas as $area) :
        $area_id = $area->term_id;
        $area_title = $area->name;
        $area_slug = $area->slug;
        $product_area_taxonomy_image = !empty(get_field('product_area_taxonomy_image', 'term_' . $area_id)) ? get_field('product_area_taxonomy_image', 'term_' . $area_id) : null;
      ?>

        <div class="<?= $prefix . '-item' ?> ">
          <a href="/" class="<?= $prefix . '-item-link' ?>">
            <div class="<?= $prefix . '-image-container' ?>">

              <img src="<?= $product_area_taxonomy_image['url'] ?>" class="<?= $prefix . '-image' ?>" />
            </div>
          </a>
          <div class="<?= $prefix . '-item-info' ?>">
            <h3><?= $area_title ?></h3>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right">
              <path d="M5 12h14M12 5l7 7-7 7" />
            </svg>
          </div>

        </div>

      <?php endforeach ?>
    </div>

  </div>
  <!-- Navigation Arrows -->
  <div class="<?= $prefix . '-nav wrapper' ?>">
    <button class="<?= $prefix . '-nav-prev' ?>" aria-label="Föregående">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right" style="transform: rotate(180deg);">
        <path d="M5 12h14M12 5l7 7-7 7" />
      </svg>
    </button>
    <button class="<?= $prefix . '-nav-next ' ?>" aria-label="Nästa">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right">
        <path d="M5 12h14M12 5l7 7-7 7" />
      </svg>
    </button>
  </div>
</section>