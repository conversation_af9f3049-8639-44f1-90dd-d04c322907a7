<?php

/**
 * ACF-fält för smort kategori slider block
 */

if (function_exists('acf_add_local_field_group')) {
  acf_add_local_field_group(array(
    'key' => 'group_smort_product_area_slider',
    'title' => 'smort product_area Slider Settings',
    'fields' => array(
      array(
        'key' => 'field_smort_product_area_slider_title',
        'label' => 'Titel',
        'name' => 'title',
        'type' => 'text',
        'instructions' => 'Ange en titel för produktsliderns sektion.',
        'default_value' => 'Produkter utifrån omdråde',
        'placeholder' => 'Produkter utifrån område',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
      array(
        'key' => 'field_smort_product_area_slider_cta',
        'label' => 'Topp-länk',
        'name' => 'top_link',
        'type' => 'link',
        'instructions' => 'Ange en länk för att gå till exempelvis "alla områden"',
        'default_value' => '/shop',
        'placeholder' => 'Visa alla områden',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
      array(
        'key' => 'field_smort_product_area_slider_selected_product_areas',
        'label' => 'Välj specifika produkter',
        'name' => 'selected_product_areas',
        'instructions' => '',
        'required' => 0,
        'type' => 'taxonomy',
        'taxonomy' => 'produktomrade',
        'field_type' => 'multi_select',
        'allow_null' => 1,
        'filters' => array('search'),
        'elements' => array('featured_image'),
        'min' => 0,
        'max' => '',
        'return_format' => 'id',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
    ),
    'location' => array(
      array(
        array(
          'param' => 'block',
          'operator' => '==',
          'value' => 'acf/smort-product-area-slider',
        ),
      ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => '',
    'show_in_rest' => 0,
  ));
}
