


.smort-product-areas {
  background-color: var(--primary-color);
  padding-block: 2rem;
  overflow:hidden;
  /* Section title */
  .smort-product-areas-title-container  {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--white);
  }


  /* Product Area Slider*/
  .smort-product-areas-container {
    position: relative;
    width: 100%;
    overflow: hidden;
   
    .smort-product-areas-track {
      display: flex;
      flex-wrap: nowrap;
      gap: 10px;
      width: 100%;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      cursor: grab;
      padding-bottom: 10px;
      position: relative;
    }


    .smort-product-areas-item {
      flex: 0 0 calc(25% - 7.5px); /* 4 produkter i desktop med 10px gap */
      background-color: transparent;
      border: none;
      padding: 0.5rem;
      display: flex;
      flex-direction: column;
      scroll-snap-align: start;
      min-width: 0; /* Förhindrar att innehållet expanderar utanför containern */

      &:first-child {
        padding-left: 1.5rem;
      }

      &:last-child {
        padding-right: 1.5rem;
      }

      .smort-product-areas-item-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        color: var(--white);
        svg {
          stroke: var(--white)
        }
      }
      .smort-product-areas-item-link {
        display: block;
        width: 100%;
        text-decoration: none;
        cursor: pointer;
        z-index: 1;
      }

      /* Produkt-bild container */
      .smort-product-areas-image-container {
        position: relative;
        width: 100%;
        padding-bottom: 100%;
        background-color: transparent;
        overflow: hidden;
        margin-bottom: 10px;
        border-radius: var(--border-radius-sm);
      }

      /* Produkt-bild */
      .smort-product-areas-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover; /* Ändrat till cover enligt önskemål */
        padding: 0;
        transition: transform 0.3s ease;
        cursor: pointer;
        scale: 1.02;
      }
    }
  }


  /* Navigation arrows */
  .smort-product-areas-nav {
    position: relative;
    display: flex;
    gap: 10px;
    z-index: 20;
    justify-content: flex-end;
    margin-bottom: 15px;

    .smort-product-areas-nav-prev,
    .smort-product-areas-nav-next {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: transparent;
      border: 1px solid var(--primary-color);
      background-color: var(--white);
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #000;
      padding: 0;
      svg {
        stroke: var(--black);
      }
    }

    .smort-product-areas-nav-prev:hover,
    .smort-product-areas-nav-next:hover {
      background-color: #f5f5f5;
      color: #000;
      svg {
        stroke: #000
      }
    }

    .smort-product-areas-nav-prev:focus,
    .smort-product-areas-nav-next:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    }
  }


  @media (max-width: 1024px) {
    
  }
  @media (max-width: 768px) {
    
  }
  @media (max-width: 480px) {

  }
}