<?php

/**
 * ACF-fält för Smort Media Text Block block
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_smort_media_text_block_fields',
        'title' => 'Smort Media Text Block Block Fields',
        'fields' => array(
            array(
                'key' => 'field_smort_media_text_block_text',
                'label' => 'Text',
                'name' => 'text',
                'type' => 'text',
                'instructions' => 'Enter the text for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_smort_media_text_block_textarea',
                'label' => 'Textarea',
                'name' => 'textarea',
                'type' => 'textarea',
                'instructions' => 'Enter the textarea for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter textarea...',
                'maxlength' => '',
                'rows' => 4,
                'new_lines' => 'wpautop',
            ),
            array(
                'key' => 'field_smort_media_text_block_image',
                'label' => 'Image',
                'name' => 'image',
                'type' => 'image',
                'instructions' => 'Enter the image for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
            ),
            array(
                'key' => 'field_smort_media_text_block_true_false',
                'label' => 'True_false',
                'name' => 'true_false',
                'type' => 'true_false',
                'instructions' => 'Enter the true_false for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 0,
                'ui' => 0,
                'ui_on_text' => '',
                'ui_off_text' => '',
            )
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/smort-media-text-block',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => 'Fields for the Smort Media Text Block block',
    ));
}
