body.archive {
  background-color: var(--accentcolor3);
  main {
  }
  ul.products {
    margin-inline: var(--side-margin);
    display:grid;
    grid-auto-flow: dense;
    gap:1rem;
    &::before {
      content:none;
    }
    grid-template-columns: repeat(6, minmax(10px,1fr));
    
    @media (width <= 1920px) {
      grid-template-columns: repeat(5, minmax(10px,1fr));
    }
    @media (width <= 1620px) {
      grid-template-columns: repeat(4, minmax(10px,1fr));
    }
    @media (width <= 1300px) {
      grid-template-columns: repeat(3, minmax(10px,1fr));
    }
    @media (width <= 997px) {
      grid-template-columns: repeat(3, minmax(10px,1fr));
    }
    @media (width <= 667px) {
      grid-template-columns: repeat(1, minmax(10px,1fr));
    }
  }
  .woocommerce-products-header {
    margin-inline: var(--side-margin);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    margin-bottom: 24px;

    padding-bottom: 32px;
    border-bottom: 1px solid var(--black);
    .woocommerce-products-header-information:has(+ .woocommerce-products-header-image) {
      width: 45%;
    }

    .woocommerce-products-header-image {
      width:45%;
      max-height: 260px;
      overflow:hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--border-radius-sm);
      img {
        object-fit:contain;
        width:100%;
        height:auto;
      }
    }
  }
}
