<?php

use function PHPSTORM_META\map;

// Include the React-like component system
require_once get_stylesheet_directory() . '/components/index.php';

function enqueue_custom_styles_scripts()
{
	wp_enqueue_style('child-theme-css', get_stylesheet_directory_uri() . '/style.css', array(), '1.0', 'all');
	wp_enqueue_style('child-theme-header-css', get_stylesheet_directory_uri() . '/header.css', array(), '1.0', 'all');
	wp_enqueue_style('child-theme-footer-css', get_stylesheet_directory_uri() . '/footer.css', array(), '1.0', 'all');
	wp_enqueue_style('single-product-css', get_stylesheet_directory_uri() . '/single-product.css', array(), '1.0', 'all');
	wp_enqueue_style('content-product-css', get_stylesheet_directory_uri() . '/content-product.css', array(), '1.0', 'all');
	wp_enqueue_style('archive-product-css', get_stylesheet_directory_uri() . '/archive-product.css', array(), '1.0', 'all');
	wp_enqueue_script('custom-accordion', get_stylesheet_directory_uri() . '/js/custom-accordion.js', array('jquery'), '1.0', true);
	wp_enqueue_script('custom-variation-script', get_template_directory_uri() . '/js/custom-variation-script.js', array('jquery'), false, true);
}
add_action('wp_enqueue_scripts', 'enqueue_custom_styles_scripts');

add_action('admin_head', 'custom_admin_css');
function custom_admin_css()
{
	echo '<style>
    html :where(.wp-block) {
				max-width:unset;
			}
  </style>';
}


add_action('acf/include_fields', function () {
	if (! function_exists('acf_add_local_field_group')) {
		return;
	}

	acf_add_local_field_group(array(
		'key' => 'group_2',
		'title' => 'Footerinställningar',
		'fields' => array(
			array(
				'key' => 'field_12',
				'label' => 'Footersida',
				'name' => 'footer_page',
				'aria-label' => '',
				'type' => 'page_link',
				'instructions' => 'Välj en sida att visa i sidfoten',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'post_type' => array(),
				'taxonomy' => array(),
				'allow_null' => 0,
				'multiple' => 0,
				'allow_archives' => 1,
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'options_page',
					'operator' => '==',
					'value' => 'temainstallningar',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 0,
	));

	acf_add_local_field_group(array(
		'key' => 'group_1',
		'title' => 'Headerinställningar',
		'fields' => array(
			array(
				'key' => 'field_1',
				'label' => 'Logotyp',
				'name' => 'logo',
				'aria-label' => '',
				'type' => 'image',
				'instructions' => 'Ladda upp webbplatsens logotyp',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'return_format' => 'url',
				'preview_size' => 'medium',
				'library' => 'all',
				'min_width' => 0,
				'min_height' => 0,
				'min_size' => 0,
				'max_width' => 0,
				'max_height' => 0,
				'max_size' => 0,
				'mime_types' => '',
			),
			array(
				'key' => 'field_2',
				'label' => 'Primärfärg',
				'name' => 'primary_color',
				'aria-label' => '',
				'type' => 'color_picker',
				'instructions' => 'Välj webbplatsens primärfärg',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'enable_opacity' => false,
				'return_format' => 'string',
			),
			array(
				'key' => 'field_3',
				'label' => 'Sekundärfärg',
				'name' => 'secondary_color',
				'aria-label' => '',
				'type' => 'color_picker',
				'instructions' => 'Välj webbplatsens sekundärfärg',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'enable_opacity' => false,
				'return_format' => 'string',
			),
			array(
				'key' => 'field_4',
				'label' => 'Headerstil',
				'name' => 'header_style',
				'aria-label' => '',
				'type' => 'radio',
				'instructions' => 'Välj headerstil',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'choices' => array(
					'style_1' => 'Header stil 1',
					'style_2' => 'Header stil 2',
					'style_3' => 'Header stil 3',
				),
				'layout' => 'vertical',
				'default_value' => '',
				'other_choice' => 0,
				'save_other_choice' => 0,
				'allow_null' => 0,
				'return_format' => 'value',
			),
			array(
				'key' => 'field_5',
				'label' => 'Transparent header',
				'name' => 'transparent_header',
				'aria-label' => '',
				'type' => 'true_false',
				'instructions' => 'Gör headern genomskinlig',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'ui' => 1,
				'default_value' => 0,
				'message' => '',
				'ui_on_text' => '',
				'ui_off_text' => '',
			),
			array(
				'key' => 'field_6',
				'label' => 'Logotyphöjd',
				'name' => 'logo_height',
				'aria-label' => '',
				'type' => 'range',
				'instructions' => 'Justera logotypens höjd',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'min' => 30,
				'max' => 200,
				'step' => 1,
				'default_value' => 100,
				'prepend' => '',
				'append' => '',
			),
			array(
				'key' => 'field_7',
				'label' => 'Typsnitt för rubriker',
				'name' => 'heading_font',
				'aria-label' => '',
				'type' => 'file',
				'instructions' => 'Ladda upp ett typsnitt för rubriker',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'return_format' => 'url',
				'library' => 'all',
				'mime_types' => 'ttf,otf,woff,woff2',
				'min_size' => 0,
				'max_size' => 0,
			),
			array(
				'key' => 'field_8',
				'label' => 'Typsnitt för innehåll',
				'name' => 'content_font',
				'aria-label' => '',
				'type' => 'file',
				'instructions' => 'Ladda upp ett typsnitt för innehåll',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'return_format' => 'url',
				'library' => 'all',
				'mime_types' => 'ttf,otf,woff,woff2',
				'min_size' => 0,
				'max_size' => 0,
			),
			array(
				'key' => 'field_9',
				'label' => 'Aktivera topbar',
				'name' => 'enable_topbar',
				'aria-label' => '',
				'type' => 'true_false',
				'instructions' => 'Aktivera topbaren',
				'required' => 0,
				'conditional_logic' => false,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'ui' => 1,
				'default_value' => 0,
				'message' => '',
				'ui_on_text' => '',
				'ui_off_text' => '',
			),
			array(
				'key' => 'field_10',
				'label' => 'Topbar text',
				'name' => 'topbar_text',
				'aria-label' => '',
				'type' => 'text',
				'instructions' => 'Text för topbaren',
				'required' => 0,
				'conditional_logic' => array(
					array(
						array(
							'field' => 'field_9',
							'operator' => '==',
							'value' => '1',
						),
					),
				),
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'maxlength' => '',
				'placeholder' => '',
				'prepend' => '',
				'append' => '',
			),
			array(
				'key' => 'field_11',
				'label' => 'Topbar bakgrundsfärg',
				'name' => 'topbar_background_color',
				'aria-label' => '',
				'type' => 'color_picker',
				'instructions' => 'Välj bakgrundsfärg för topbaren',
				'required' => 0,
				'conditional_logic' => array(
					array(
						array(
							'field' => 'field_9',
							'operator' => '==',
							'value' => '1',
						),
					),
				),
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'enable_opacity' => false,
				'return_format' => 'string',
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'options_page',
					'operator' => '==',
					'value' => 'temainstallningar',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 0,
	));
});

add_action('acf/init', function () {
	acf_add_options_page(array(
		'page_title' => 'Temainställningar',
		'menu_slug' => 'temainstallningar',
		'position' => '',
		'redirect' => false,
		'menu_icon' => array(
			'type' => 'dashicons',
			'value' => 'dashicons-admin-generic',
		),
		'update_button' => 'Update',
		'updated_message' => 'Options Updated',
		'icon_url' => 'dashicons-admin-generic',
	));
});

/* Register blocks */
function register_acf_block_types()
{
	acf_register_block_type(array(
		'name' => 'smort-media-text-block',
		'title' => __("Smort - Smort Media Text Block"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-media-text-block/smort-media-text-block.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-media-text-block/smort-media-text-block.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-media-text-block/smort-media-text-block.js'
	));
	acf_register_block_type(array(
		'name' => 'smort-hero-banner',
		'title' => __("Smort - Smort Hero Banner"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-hero-banner/smort-hero-banner.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-hero-banner/smort-hero-banner.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-hero-banner/smort-hero-banner.js'
	));


	acf_register_block_type(array(
		'name' => 'Smort - Kategorier',
		'title' => __("Smort - Katergorier"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.js'
	));
	acf_register_block_type(array(
		'name' => 'Smort - Callout Hero',
		'title' => __("Smort - Kortgrid Hero"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-callout-hero/smort-callout-hero.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-callout-hero/smort-callout-hero.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-callout-hero/smort-callout-hero.js'
	));
	acf_register_block_type(array(
		'name' => 'smort-product-slider',
		'title' => __("Smort - Product Slider"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-product-slider/smort-product-slider.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.js'
	));
	acf_register_block_type(array(
		'name' => 'smort-product-area-slider',
		'title' => __("Smort - Produktområde Slider"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-product-area-slider/smort-product-area-slider.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-area-slider/smort-product-area-slider.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-area-slider/smort-product-area-slider.js'
	));
};

// Check if function exists and hook into setup.
if (function_exists('acf_register_block_type')) {
	add_action('acf/init', 'register_acf_block_types');
}

/* Include ACF field groups */
require_once get_stylesheet_directory() . '/template-parts/smortblocks/smort-media-text-block/acf/smort-media-text-block.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/smort-hero-banner/acf/smort-hero-banner.php';



/* Registrera block */

// Ladda ACF-fält för Smort Product Slider
require_once get_stylesheet_directory() . '/template-parts/smortblocks/smort-product-slider/acf/smort-product-slider-acf.php';
// Ladda ACF-fält för Smort Category Slider
require_once get_stylesheet_directory() . '/template-parts/smortblocks/smort-product-area-slider/acf/smort-product-area-slider-acf.php';


// Remove woocommerce default ordering on archive
remove_action('woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30);
remove_action('woocommerce_before_shop_loop', 'woocommerce_result_count', 20);
